import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue2";
import commonjs from "vite-plugin-commonjs";
import Components from "unplugin-vue-components/vite";
import { resolve } from "path";

export default defineConfig({
    plugins: [
        vue(),
        commonjs(),
        Components({})
    ],
    optimizeDeps: {
        // 强制预构建某些依赖
        include: ['element-ui', 'axios', 'js-cookie'],
        // 排除某些依赖的预构建
        exclude: []
    },
    resolve: {
        alias: {
            '@': resolve(__dirname, 'src'),
            // 配置node_modules中的别名
            'element-ui': resolve(__dirname, 'node_modules/element-ui'),
            'ace-builds': resolve(__dirname, 'node_modules/ace-builds'),
            // 或者配置到具体的子目录
            'ace-src': resolve(__dirname, 'node_modules/ace-builds/src-noconflict')
        }
    },
    server: {
        host: '0.0.0.0', // 允许外部访问
        port: 5173,      // 指定端口
        strictPort: false, // 如果端口被占用，自动尝试下一个可用端口
        open: false      // 不自动打开浏览器
    }
})
