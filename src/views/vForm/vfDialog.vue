<template>
  <!-- v-dialog-drag: 可拖动 -->
  <el-dialog
    v-if="previewFlag"
    :visible.sync="previewFlag"
    show-close
    :close-on-click-modal="false"
    center
    class="small-padding-dialog"
    width="75%"
    append-to-body
    @close="close"
  >
    <VFormRender v-if="formJson" ref="preForm" :form-json="formJson" :form-data="formDataJson" />
  </el-dialog>
</template>

<script>
import VFormRender from '@/components/form-render/index.vue'
import CodeEditor from '@/components/code-editor/index.vue'
import i18n from '@/utils/i18n'
export default {
  name: 'vfDialog',
  components: {
    VFormRender,
    CodeEditor
  },
  mixins: [i18n],
  data() {
    return {
      previewFlag: false, // 预览弹窗显示的flag
      formJson: null, // form表单的json串
      formDataFlag: false, // 获取数据弹窗显示的flag
      formDataJson: {}, // 获取数据弹窗的json串
      dataKey: null
    }
  },
  methods: {
    open(hasOpen, formJson, dataKey) {
      this.previewFlag = hasOpen
      if (hasOpen) {
        this.dataKey = dataKey
        this.formJson = formJson
      }
    },
    close(){
      localStorage.removeItem(this.dataKey)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
