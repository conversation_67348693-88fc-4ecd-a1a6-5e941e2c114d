<template>
  <el-scrollbar class="side-scroll-bar" :style="{ height: scrollerHeight }">
    <div class="panel-container">
      <el-tabs v-model="firstTab" class="no-bottom-margin indent-left-margin">
        <el-tab-pane name="componentLib">
          <span slot="label"
            ><i class="el-icon-set-up"></i>
            {{ i18nt('designer.componentLib') }}</span
          >

          <el-collapse v-model="activeNames" class="widget-collapse">
            <el-collapse-item
              name="1"
              :title="i18nt('designer.containerTitle')"
            >
              <draggable
                tag="ul"
                :list="containers"
                :group="{ name: 'dragGroup', pull: 'clone', put: false }"
                :clone="handleContainerWidgetClone"
                ghost-class="ghost"
                :sort="false"
                :move="checkContainerMove"
                @end="onContainerDragEnd"
              >
                <li
                  v-for="(ctn, index) in containers"
                  :key="index"
                  class="container-widget-item"
                  :title="ctn.displayName"
                  @dblclick="addContainerByDbClick(ctn)"
                >
                  <span
                    ><svg-icon :icon-class="ctn.icon" />{{
                      i18n2t(
                        `designer.widgetLabel.${ctn.type}`,
                        `extension.widgetLabel.${ctn.type}`,
                      )
                    }}</span
                  >
                </li>
              </draggable>
            </el-collapse-item>

            <el-collapse-item
              name="2"
              :title="i18nt('designer.basicFieldTitle')"
            >
              <draggable
                tag="ul"
                :list="basicFields"
                :group="{ name: 'dragGroup', pull: 'clone', put: false }"
                :clone="handleFieldWidgetClone"
                ghost-class="ghost"
                :sort="false"
              >
                <li
                  v-for="(fld, index) in basicFields"
                  :key="index"
                  class="field-widget-item"
                  :title="fld.displayName"
                  @dblclick="addFieldByDbClick(fld)"
                >
                  <span
                    ><svg-icon :icon-class="fld.icon" />{{
                      i18n2t(
                        `designer.widgetLabel.${fld.type}`,
                        `extension.widgetLabel.${fld.type}`,
                      )
                    }}</span
                  >
                </li>
              </draggable>
            </el-collapse-item>

            <el-collapse-item
              name="3"
              :title="i18nt('designer.advancedFieldTitle')"
            >
              <draggable
                tag="ul"
                :list="advancedFields"
                :group="{ name: 'dragGroup', pull: 'clone', put: false }"
                :clone="handleFieldWidgetClone"
                ghost-class="ghost"
                :sort="false"
              >
                <li
                  v-for="(fld, index) in advancedFields"
                  :key="index"
                  class="field-widget-item"
                  :title="fld.displayName"
                  @dblclick="addFieldByDbClick(fld)"
                >
                  <span
                    ><svg-icon :icon-class="fld.icon" />{{
                      i18n2t(
                        `designer.widgetLabel.${fld.type}`,
                        `extension.widgetLabel.${fld.type}`,
                      )
                    }}</span
                  >
                </li>
              </draggable>
            </el-collapse-item>

            <!-- -->
            <!-- <el-collapse-item
              name="4"
              :title="i18nt('designer.customFieldTitle')"
            >
              <draggable
                tag="ul"
                :list="customFields"
                :group="{ name: 'dragGroup', pull: 'clone', put: false }"
                :clone="handleFieldWidgetClone"
                ghost-class="ghost"
                :sort="false"
              >
                <li
                  v-for="(fld, index) in customFields"
                  :key="index"
                  class="field-widget-item"
                  :title="fld.displayName"
                  @dblclick="addFieldByDbClick(fld)"
                >
                  <span>
                    <svg-icon :icon-class="fld.icon" />{{
                      i18n2t(
                        `designer.widgetLabel.${fld.type}`,
                        `extension.widgetLabel.${fld.type}`,
                      )
                    }}</span
                  >
                </li>
              </draggable>
            </el-collapse-item> -->
            <!-- -->
          </el-collapse>
        </el-tab-pane>

        <el-tab-pane name="viewLib">
          <span slot="label"
            ><i class="el-icon-c-scale-to-original"></i>
            {{ i18nt('designer.viewLib') }}</span
          >
          <el-collapse v-model="activeNames2" class="widget-collapse">
            <el-collapse-item
              name="1"
              :title="i18nt('designer.viewLayoutTitle')"
            >
              <draggable
                tag="ul"
                :list="viewLayoutFields"
                :group="{ name: 'dragGroup', pull: 'clone', put: false }"
                :clone="handleContainerWidgetClone"
                ghost-class="ghost"
                :sort="false"
              >
                <li
                  v-for="(fld, index) in viewLayoutFields"
                  :key="index"
                  class="container-widget-item"
                  :title="fld.displayName"
                  @dblclick="addFieldByDbClick(fld)"
                >
                  <span
                    ><svg-icon :icon-class="fld.icon" />{{
                      i18n2t(
                        `designer.widgetLabel.${fld.type}`,
                        `extension.widgetLabel.${fld.type}`,
                      )
                    }}</span
                  >
                </li>
              </draggable>
            </el-collapse-item>
            <el-collapse-item
              name="2"
              :title="i18nt('designer.viewFieldTitle')"
            >
              <draggable
                tag="ul"
                :list="viewFields"
                :group="{ name: 'dragGroup', pull: 'clone', put: false }"
                :clone="handleFieldWidgetClone"
                ghost-class="ghost"
                :sort="false"
              >
                <li
                  v-for="(fld, index) in viewFields"
                  :key="index"
                  class="container-widget-item"
                  :title="fld.displayName"
                  @dblclick="addFieldByDbClick(fld)"
                >
                  <span
                    ><svg-icon :icon-class="fld.icon" />{{
                      i18n2t(
                        `designer.widgetLabel.${fld.type}`,
                        `extension.widgetLabel.${fld.type}`,
                      )
                    }}</span
                  >
                </li>
              </draggable>
            </el-collapse-item>
            <el-collapse-item
              name="3"
              :title="i18nt('designer.viewIntegrateTitle')"
            >
              <draggable
                tag="ul"
                :list="viewIntegrateFields"
                :group="{ name: 'dragGroup', pull: 'clone', put: false }"
                :clone="handleFieldWidgetClone"
                ghost-class="ghost"
                :sort="false"
              >
                <li
                  v-for="(fld, index) in viewIntegrateFields"
                  :key="index"
                  class="container-widget-item"
                  :title="fld.displayName"
                  @dblclick="addFieldByDbClick(fld)"
                >
                  <span
                    ><svg-icon :icon-class="fld.icon" />{{
                      i18n2t(
                        `designer.widgetLabel.${fld.type}`,
                        `extension.widgetLabel.${fld.type}`,
                      )
                    }}</span
                  >
                </li>
              </draggable>
            </el-collapse-item>
          </el-collapse>
        </el-tab-pane>
        <el-tab-pane name="template">
          <span slot="label"
            ><i class="el-icon-c-scale-to-original"></i>预设模板</span
          >
          <el-collapse class="widget-collapse" v-model="collapseIndexs">
            <el-collapse-item
              v-for="tem in temArr"
              :key="tem"
              :name="tem"
              :title="tem"
            >
              <ul>
                <li
                  v-for="item in templateArr.filter(
                    (item) => item.templateBusType == tem,
                  )"
                  :key="item.id"
                   class="container-widget-item"
                   style="cursor: pointer;"
                  :title="item.templateName"
                  @click="preTempalte(item.templateJson)"
                >
                  <span
                    ><svg-icon icon-class="text-field" />
                    {{ item.templateName }}</span
                  >
                </li>
              </ul>
            </el-collapse-item>
          </el-collapse>
        </el-tab-pane>
      </el-tabs>
      <el-dialog
        title="预设模板"
        width="90%"
        :visible="visible"
        @close="visible = false"
      >
        <div style="display: flex; height: 600px">
          <div style="width: 270px; height: 100%; overflow: auto">
            <el-collapse class="widget-collapse" v-model="collapseIndexs2">
              <el-collapse-item
                v-for="tem in temArr"
                :key="tem"
                :name="tem"
                :title="tem"
              >
                <ul>
                  <li
                    v-for="item in templateArr.filter(
                      (item) => item.templateBusType == tem,
                    )"
                    :key="item.id"
                    class="tempalte"
                    :title="item.templateName"
                    @click="preTempalte(item.templateJson)"
                  >
                    <span
                      ><svg-icon icon-class="text-field" />
                      {{ item.templateName }}</span
                    >
                  </li>
                </ul>
              </el-collapse-item>
            </el-collapse>
          </div>
          <div style="flex: 1; overflow: auto">
            <v-form-render
              :form-data="{}"
              ref="vForm"
              v-if="formDataJson"
              :form-json="formDataJson"
            ></v-form-render>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button type="info" @click="visible = false">取消</el-button>
          <el-button type="primary" @click="loadTempalte">使用模板</el-button>
        </div>
      </el-dialog>
    </div>
  </el-scrollbar>
</template>

<script>
import Draggable from 'vuedraggable';
import VFormRender from '@/components/form-render/index.vue';
import {
  containers,
  basicFields,
  advancedFields,
  customFields,
  viewFields,
  viewLayoutFields,
  viewIntegrateFields,
} from './widgetsConfig';
import { addWindowResizeHandler } from '@/utils/util';
import i18n from '@/utils/i18n';
import { getFormTempalte } from '@/api/tool/form';

export default {
  name: 'FieldPanel',
  mixins: [i18n],
  components: {
    Draggable,
    VFormRender,
  },
  props: {
    designer: Object,
  },
  data() {
    return {
      firstTab: 'componentLib',
      visible: false,
      scrollerHeight: 0,
      formDataJson: null,
      activeNames: ['1', '2', '3', '4'],
      activeNames2: ['1', '2', '3', '4'],
      templateArr: [],
      collapseIndexs: [],
      collapseIndexs2: [],
      temArr: [],
      containers,
      basicFields,
      advancedFields,
      customFields,
      viewFields,
      viewLayoutFields,
      viewIntegrateFields,
    };
  },
  mounted() {
    this.getTempalte();
    this.loadWidgets();

    this.scrollerHeight = window.innerHeight - 156 + 'px';
    addWindowResizeHandler(() => {
      this.$nextTick(() => {
        this.scrollerHeight = window.innerHeight - 156 + 'px';
      });
    });
  },
  methods: {
    preTempalte(json) {
      if (!this.visible) {
        this.visible = true;
      }
      this.formDataJson = null;
      this.$nextTick(() => {
        this.formDataJson = JSON.parse(json);
      });
    },
    open() {
      setTimeout(() => {
        this.visible = true;
      }, 200);
    },
    loadTempalte() {
      if (this.designer.widgetList.length == 0) {
        this.designer.loadFormJson(this.formDataJson);
        this.visible = false;
      } else {
        this.$confirm('此操作将覆盖当前表单, 是否继续?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then((res) => {
            this.designer.loadFormJson(this.formDataJson);
            this.visible = false;
          })
          .catch((err) => {});
      }
    },
    getTempalte() {
      getFormTempalte().then((res) => {
        this.templateArr = res.data;
        this.temArr = [
          ...new Set(this.templateArr.map((item) => item.templateBusType)),
        ];
        this.collapseIndexs = this.temArr.map((item, index) => item);
        this.collapseIndexs2 = this.temArr.map((item, index) => item);
      });
    },
    loadWidgets() {
      this.containers = this.containers
        .map((con) => {
          return {
            ...con,
            displayName: this.i18n2t(
              `designer.widgetLabel.${con.type}`,
              `extension.widgetLabel.${con.type}`,
            ),
          };
        })
        .filter((con) => {
          return !con.internal;
        });

      this.basicFields = this.basicFields.map((fld) => {
        return {
          ...fld,
          displayName: this.i18n2t(
            `designer.widgetLabel.${fld.type}`,
            `extension.widgetLabel.${fld.type}`,
          ),
        };
      });

      this.advancedFields = this.advancedFields.map((fld) => {
        return {
          ...fld,
          displayName: this.i18n2t(
            `designer.widgetLabel.${fld.type}`,
            `extension.widgetLabel.${fld.type}`,
          ),
        };
      });

      this.customFields = this.customFields.map((fld) => {
        return {
          ...fld,
          displayName: this.i18n2t(
            `designer.widgetLabel.${fld.type}`,
            `extension.widgetLabel.${fld.type}`,
          ),
        };
      });

      this.viewFields = this.viewFields.map((fld) => {
        return {
          ...fld,
          displayName: this.i18n2t(
            `designer.widgetLabel.${fld.type}`,
            `extension.widgetLabel.${fld.type}`,
          ),
        };
      });
    },

    handleContainerWidgetClone(origin) {
      return this.designer.copyNewContainerWidget(origin);
    },

    handleFieldWidgetClone(origin) {
      return this.designer.copyNewFieldWidget(origin);
    },

    checkContainerMove(evt) {
      return this.designer.checkWidgetMove(evt);
    },

    onContainerDragEnd(evt) {
      //console.log('Drag end of container: ')
      //console.log(evt)
    },

    addContainerByDbClick(container) {
      this.designer.addContainerByDbClick(container);
    },

    addFieldByDbClick(widget) {
      this.designer.addFieldByDbClick(widget);
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .danger {
  color: #fff;
  background-color: #f56c6c;
  border-color: #f56c6c;
}
::v-deep .el-tabs__nav {
  margin-left: 0;
}
::v-deep .el-tabs__item {
  padding: 0 8px;
  span{
    font-size: 13px;
  }
}
.side-scroll-bar {
  ::v-deep .el-scrollbar__wrap {
    overflow-x: hidden;
  }
}

div.panel-container {
  padding-bottom: 10px;
}

.no-bottom-margin ::v-deep .el-tabs__header {
  margin-bottom: 0;
}

.indent-left-margin {
  ::v-deep .el-tabs__nav {
    margin-left: 10px;
  }
}

.el-collapse-item ::v-deep ul > li {
  list-style: none;
}

.widget-collapse {
  border-top-width: 0;

  ::v-deep .el-collapse-item__header {
    margin-left: 8px;
    font-style: italic;
    font-weight: bold;
  }

  ::v-deep .el-collapse-item__content {
    padding-bottom: 6px;
    ul {
      padding-left: 10px; /* 重置IE11默认样式 */
      margin: 0; /* 重置IE11默认样式 */
      margin-block-start: 0;
      margin-block-end: 0.25em;
      padding-inline-start: 10px;

      &:after {
        content: '';
        display: block;
        clear: both;
      }

      .container-widget-item,
      .field-widget-item {
        display: inline-block;
        height: 28px;
        line-height: 28px;
        width: 100px;
        border: 1px solid #dcdfe6;
        margin: 0 5px 5px 0;
        cursor: move;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        color: #fefefe;
        border-radius: 5px;

        svg {
          margin-left: 5px;
          margin-right: 5px;
        }
      }

      .container-widget-item:hover,
      .field-widget-item:hover {
        background: #114996;
        border-color: #409eff;
      }

      .drag-handler {
        position: absolute;
        top: 0;
        left: 160px;
        background-color: #dddddd;
        border-radius: 5px;
        padding-right: 5px;
        font-size: 11px;
        color: #666666;
      }
    }
  }
}

.el-card.ft-card {
  border: 1px solid #8896b3;
}

.ft-card {
  margin-bottom: 10px;

  .bottom {
    margin-top: 10px;
    line-height: 12px;
  }

  /*
    .image-zoom {
      height: 500px;
      width: 620px
    }
    */

  .ft-title {
    font-size: 13px;
    font-weight: bold;
  }

  .right-button {
    padding: 0;
    float: right;
  }

  .clear-fix:before,
  .clear-fix:after {
    display: table;
    content: '';
  }

  .clear-fix:after {
    clear: both;
  }
}
</style>
