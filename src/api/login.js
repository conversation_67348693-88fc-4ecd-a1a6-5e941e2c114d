import request from '@/utils/request';

// 登录方法
export function login(username, password, code, uuid, type) {
  return request({
    url: '/auth/login',
    method: 'post',
    data: { username, password, code, uuid, type},
  });
}

// 刷卡登录方法
export function cardLogin(userInfo) {
  return request({
    url: '/auth/cardLogin',
    method: 'post',
    data: userInfo,
  });
}

// 注册方法
export function register(data) {
  return request({
    url: '/auth/register',
    headers: {
      isToken: false,
    },
    method: 'post',
    data: data,
  });
}

// 刷新方法
export function refreshToken() {
  return request({
    url: '/auth/refresh',
    method: 'post',
  });
}

// 获取用户详细信息
export function getInfo(type) {
  return request({
    url: '/system/user/getInfo',
    method: 'get',
    params: { type },
  });
}

// 退出方法
export function logout() {
  return request({
    url: '/auth/logout',
    method: 'delete',
  });
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/code',
    method: 'get',
  });
}

// 获取密钥
export function getPublicKey(isWebLogin, username) {
  return request({
    url: '/auth/publicKey',
    method: 'get',
    params: { isWebLogin, username },
  });
}

/**
 * 获取登录页的配置信息
 * @returns
 */
export function getSysConfig(params) {
  return request({
    url: '/interfaces/DRLine/config/getConfig',
    method: 'get',
    params,
  });
}

export function getImageBase64(id) {
  return request({
    url: `/file/fileCloud/file/${id}`,
    method: 'get',
    responseType: 'blob',
  });
}

/**
 * 修改登录页配置
 * @returns
 */
export function setSysConfig(data) {
  return request({
    url: '/interfaces/DRLine/config/setConfig',
    method: 'put',
    data,
  });
}
